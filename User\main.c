#include "stm32f10x.h"                  // Device header
#include "Delay.h"
#include "OLED.h"
#include "Servo.h"
#include "Key.h"
#include "LED.h"
#include "Serial.h"
#include <string.h>
#include <stdlib.h>

// 串口接收缓冲区
char RxBuffer[20];
uint8_t RxIndex = 0;
uint8_t CommandReady = 0;

// LED闪烁控制变量
uint8_t LED_BlinkCount = 0;
uint32_t LED_BlinkTimer = 0;
uint8_t LED_BlinkState = 0;

// 当前舵机角度
uint16_t CurrentAngle = 0;

// 函数声明
void ProcessCommand(void);
void LED_BlinkTwice(void);
void LED_BlinkProcess(void);
uint8_t ParseAngle(char* command, uint16_t* angle);

int main(void)
{
	LED_Init();
	OLED_Init();
	Servo_Init();
	Serial_Init();

	// 初始化显示
	OLED_ShowString(1, 1, "ANGLE: 000");

	// 初始化串口接收缓冲区
	memset(RxBuffer, 0, sizeof(RxBuffer));
	RxIndex = 0;
	CommandReady = 0;

	while (1)
	{
		// 处理串口接收
		if (Serial_GetRxFlag() == 1)
		{
			uint8_t receivedByte = Serial_GetRxData();

			// 如果接收到换行符或回车符，表示指令结束
			if (receivedByte == '\n' || receivedByte == '\r')
			{
				if (RxIndex > 0)
				{
					RxBuffer[RxIndex] = '\0';  // 添加字符串结束符
					CommandReady = 1;          // 标记指令准备就绪
				}
			}
			// 如果是可打印字符且缓冲区未满
			else if (receivedByte >= 32 && receivedByte <= 126 && RxIndex < sizeof(RxBuffer) - 1)
			{
				RxBuffer[RxIndex] = receivedByte;
				RxIndex++;
			}
		}

		// 处理完整的指令
		if (CommandReady)
		{
			ProcessCommand();
			// 重置接收缓冲区
			memset(RxBuffer, 0, sizeof(RxBuffer));
			RxIndex = 0;
			CommandReady = 0;
		}

		// 处理LED闪烁
		LED_BlinkProcess();

		// 小延时避免CPU占用过高
		Delay_ms(1);
	}
}

/**
 * 函数：处理接收到的指令
 * 参数：无
 * 返回值：无
 */
void ProcessCommand(void)
{
	uint16_t angle;

	// 解析ANGLE指令
	if (ParseAngle(RxBuffer, &angle))
	{
		// 指令有效，更新舵机角度
		CurrentAngle = angle;
		Servo_SetAngle((float)angle);

		// 更新OLED显示
		OLED_ShowString(1, 1, "ANGLE: ");
		OLED_ShowNum(1, 8, angle, 3);

		// 触发LED闪烁两次
		LED_BlinkTwice();

		// 串口回传确认信息
		Serial_Printf("ANGLE:%d \r\n", angle);
	}
	else
	{
		// 指令无效，发送错误信息
		Serial_Printf("ERROR\r\n");
	}
}

/**
 * 函数：解析ANGLE指令
 * 参数：command - 指令字符串，angle - 输出角度值的指针
 * 返回值：1-解析成功，0-解析失败
 */
uint8_t ParseAngle(char* command, uint16_t* angle)
{
	// 检查指令是否以"ANGLE "开头
	if (strncmp(command, "ANGLE ", 6) != 0)
	{
		return 0;  // 指令格式错误
	}

	// 提取角度数值
	char* angleStr = command + 6;  // 跳过"ANGLE "

	// 检查角度字符串是否为空
	if (*angleStr == '\0')
	{
		return 0;
	}

	// 转换为整数
	int tempAngle = atoi(angleStr);

	// 验证角度范围
	if (tempAngle < 0 || tempAngle > 180)
	{
		return 0;  // 角度超出范围
	}

	*angle = (uint16_t)tempAngle;
	return 1;  // 解析成功
}

/**
 * 函数：启动LED闪烁两次
 * 参数：无
 * 返回值：无
 */
void LED_BlinkTwice(void)
{
	LED_BlinkCount = 4;  // 闪烁两次需要4个状态变化（亮-灭-亮-灭）
	LED_BlinkTimer = 0;
	LED_BlinkState = 1;  // 开始状态为亮
	LED1_ON();           // 立即点亮LED
}

/**
 * 函数：处理LED闪烁过程
 * 参数：无
 * 返回值：无
 */
void LED_BlinkProcess(void)
{
	if (LED_BlinkCount > 0)
	{
		LED_BlinkTimer++;

		// 每200ms切换一次LED状态
		if (LED_BlinkTimer >= 200)
		{
			LED_BlinkTimer = 0;
			LED_BlinkCount--;

			if (LED_BlinkState)
			{
				LED1_OFF();
				LED_BlinkState = 0;
			}
			else
			{
				LED1_ON();
				LED_BlinkState = 1;
			}

			// 闪烁完成后确保LED关闭
			if (LED_BlinkCount == 0)
			{
				LED1_OFF();
			}
		}
	}
}

#include "stm32f10x.h"                  // Device header
#include "Delay.h"
#include "OLED.h"
#include "Servo.h"
#include "Key.h"
#include "LED.h"
#include "Serial.h"

uint8_t Input[8];
float Angle;

int main(void)
{
	LED_Init();
	OLED_Init();
	Servo_Init();
	Serial_Init();
	
//	OLED_ShowString(1, 1, "ANGLE:");
	
	while (1)
	{
			if (Serial_GetRxFlag() == 1)			//检查串口接收数据的标志位
		{
//			Input=Serial_GetRxData();
			Input[0] = Serial_GetRxData();		//获取串口接收的数据
//			Serial_SendByte(Input);
			Serial_SendString(Input);			//串口将收到的数据回传回去，用于测试
		}
		Angle=Input[5]*100+Input[6]*10+Input[7];
		Servo_SetAngle(Angle);
		OLED_ShowString(1,1,Input);
//		OLED_ShowHexNum(1, 7, <PERSON><PERSON>, 3);	
		
//		if (Serial_GetRxFlag() == 1)			//检查串口接收数据的标志位
//		{
//			Angle[] = Serial_GetRxData();		//获取串口接收的数据
//			Serial_SendString(char Angle);			//串口将收到的数据回传回去，用于测试
		}
}
